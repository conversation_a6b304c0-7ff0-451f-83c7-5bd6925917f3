# Unity URP 着色器模板库

## 概述
这是一个完整的Unity URP着色器模板库，提供了多种常用的着色器模板，帮助快速开发自定义材质。

## 模板列表

### 1. URPCustomTemplate.shader - 完整功能模板
**功能特性：**
- 完整的PBR材质支持（漫反射、金属度、光滑度）
- 自定义光照计算（Lambert + Blinn-Phong）
- 完整的阴影接收和投射
- 自定义ShadowCaster、DepthOnly、DepthNormals Pass
- 可配置的渲染状态（混合模式、深度测试、剔除、模板测试）
- GPU Instancing和SRP Batcher支持
- 多光源支持

**适用场景：**
- 需要完整PBR材质的物体
- 需要精确光照和阴影的场景
- 需要自定义渲染状态的特殊材质

### 2. URPSimpleTemplate.shader - 简化模板
**功能特性：**
- 基础漫反射光照
- 主纹理和颜色支持
- 简化的渲染管线
- 最小化的变体数量
- 高性能优化
- 透明度裁剪支持

**适用场景：**
- 快速原型开发
- 性能敏感的移动平台
- 简单的材质需求

### 3. URPTransparentTemplate.shader - 透明材质模板
**功能特性：**
- 多种透明混合模式
- 软粒子效果
- 双面渲染选项
- 透明度裁剪
- 深度排序优化

**适用场景：**
- 透明和半透明材质
- 粒子效果
- UI元素
- 玻璃、水等透明物体

## 使用指南

### 基本使用步骤
1. 选择合适的模板文件
2. 复制到项目中并重命名
3. 修改Shader名称和Properties
4. 根据需求调整光照和渲染逻辑
5. 创建材质并应用到物体

### 性能优化建议
1. **选择合适的模板**：根据实际需求选择功能最少的模板
2. **减少变体**：注释掉不需要的#pragma multi_compile指令
3. **优化纹理采样**：合并纹理通道，减少采样次数
4. **使用SRP Batcher**：保持CBUFFER结构一致性

### 常用修改模式

#### 添加新的纹理属性
```hlsl
// Properties中添加
_NormalMap ("法线贴图", 2D) = "bump" {}

// CBUFFER中添加
float4 _NormalMap_ST;

// 声明纹理
TEXTURE2D(_NormalMap);
SAMPLER(sampler_NormalMap);

// 在fragment shader中采样
half3 normalTS = UnpackNormal(SAMPLE_TEXTURE2D(_NormalMap, sampler_NormalMap, input.uv));
```

#### 添加自定义光照模型
```hlsl
// 自定义光照函数
half3 CustomLighting(Light light, half3 normalWS, half3 viewDirWS, half3 albedo)
{
    float NdotL = saturate(dot(normalWS, light.direction));
    half3 diffuse = albedo * light.color * NdotL;
    
    // 添加自定义高光或其他效果
    
    return diffuse * light.shadowAttenuation;
}
```

## 注意事项

1. **文件头部注释**：所有着色器文件都包含详细的功能说明
2. **代码复用**：尽量基于现有模板修改，避免重复造轮子
3. **维护成本**：保持一种实现方法，移除重复功能
4. **测试验证**：修改后及时测试，确保功能正常

## 扩展建议

### 可添加的功能模块
- 顶点动画（风摆、波浪等）
- 高级光照模型（Toon、NPR等）
- 后处理效果（描边、发光等）
- 程序化纹理生成
- 细分曲面支持

### 性能分析工具
- Unity Frame Debugger
- RenderDoc
- Unity Profiler
- GPU Usage Profiler

## 版本历史
- v1.0 (2025-07-09): 初始版本，包含三个基础模板

## 作者信息
- 作者：晚三千
- 创建日期：2025-07-09
- 维护状态：活跃开发中
