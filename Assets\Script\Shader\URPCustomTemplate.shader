/*
 * Unity URP 自定义着色器模板
 * 功能：提供完整的URP着色器基础模板，包含自定义的ShadowCaster、DepthOnly、DepthNormals Pass
 * 作者：晚三千
 * 版本：1.0
 * 创建日期：2025-07-09
 * 
 * 特性：
 * - 支持基础PBR材质属性（漫反射、金属度、光滑度）
 * - 自定义光照计算（Lambert漫反射 + <PERSON><PERSON><PERSON>-<PERSON><PERSON>高光）
 * - 完整的阴影接收和投射
 * - 深度渲染支持
 * - 法线深度渲染支持
 * - 可选的GPU Instancing和SRP Batcher支持
 * - 可配置的渲染状态（混合模式、深度测试、剔除等）
 */

Shader "Custom/URPTemplate"
{
    Properties
    {
        [MainTexture] _MainTex ("主纹理", 2D) = "white" {}
        [MainColor]_MainColor ("主颜色", Color) = (1,1,1,1)
        _Metallic ("金属度", Range(0, 1)) = 0
        _Smoothness ("光滑度", Range(0, 1)) = 0.5

        // 渲染选项（可根据需要取消注释）
        [Header(渲染选项)]
        [Enum(UnityEngine.Rendering.BlendOp)]_BlendOp("混合操作", Float) = 0.0
        [Enum(UnityEngine.Rendering.BlendMode)]_SrcBlend("源混合", Float) = 1.0
        [Enum(UnityEngine.Rendering.BlendMode)]_DstBlend("目标混合", Float) = 0.0
        [Enum(UnityEngine.Rendering.BlendMode)]_SrcBlendAlpha("源Alpha混合", Float) = 1.0
        [Enum(UnityEngine.Rendering.BlendMode)]_DstBlendAlpha("目标Alpha混合", Float) = 0.0
        
        [Header(深度测试)]
        [ToggleUI]_ZWrite("深度写入", Float) = 1.0
        [Enum(UnityEngine.Rendering.CompareFunction)]_ZTest("深度测试", Float) = 4.0
        [Enum(UnityEngine.Rendering.CullMode)]_Cull("剔除模式", Float) = 2.0

        [Header(模板测试)]
        [IntRange]_Stencil ("模板ID", Range(0,255)) = 0
        [IntRange]_StencilWriteMask ("模板写入掩码", Range(0,255)) = 255
        [IntRange]_StencilReadMask ("模板读取掩码", Range(0,255)) = 255
        [Enum(UnityEngine.Rendering.CompareFunction)]_StencilComp("模板比较", Float) = 0.0
        [Enum(UnityEngine.Rendering.StencilOp)]_StencilOp("模板操作", Float) = 0.0
        [Enum(UnityEngine.Rendering.StencilOp)]_StencilOpFail("模板失败操作", Float) = 0.0
        [Enum(UnityEngine.Rendering.StencilOp)]_StencilOpZFail("深度失败操作", Float) = 0.0
    }

    SubShader
    {
        Tags 
        { 
            "RenderPipeline"="UniversalPipeline"
            "RenderType"="Opaque"
            "Queue"="Geometry"
        }

        Pass
        {
            Name "ForwardLit"
            Tags
            {
                "LightMode"="UniversalForward"
            }

            // 可配置的渲染状态
            BlendOp [_BlendOp]
            Blend [_SrcBlend][_DstBlend], [_SrcBlendAlpha][_DstBlendAlpha]
            ZWrite [_ZWrite]
            ZTest [_ZTest]
            Cull [_Cull]

            Stencil
            {
                Ref [_Stencil]
                Comp [_StencilComp]
                ReadMask [_StencilReadMask]
                WriteMask [_StencilWriteMask]
                Pass [_StencilOp]
                Fail [_StencilOpFail]
                ZFail [_StencilOpZFail]
            }

            HLSLPROGRAM
            #pragma target 3.5
            #pragma vertex vert
            #pragma fragment frag

            // 阴影关键字
            #pragma multi_compile _ _MAIN_LIGHT_SHADOWS
            #pragma multi_compile _ _MAIN_LIGHT_SHADOWS_CASCADE
            #pragma multi_compile _ _SHADOWS_SOFT

            // GPU Instancing（与SRP Batcher冲突，根据需要选择）
            // #pragma multi_compile_instancing

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"

            // 材质属性缓冲区（对齐到float4以优化性能）
            CBUFFER_START(UnityPerMaterial)
            half4 _MainColor;
            float _Metallic;
            float _Smoothness;
            float4 _MainTex_ST;
            CBUFFER_END

            // GPU Instancing缓冲区（可选）
            // #ifdef UNITY_INSTANCING_ENABLED
            // UNITY_INSTANCING_BUFFER_START(PerInstance)
            // UNITY_DEFINE_INSTANCED_PROP(float4, _MainColor)
            // UNITY_INSTANCING_BUFFER_END(PerInstance)
            // #endif

            struct Attributes
            {
                float4 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float2 uv : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float2 uv : TEXCOORD0;
                float3 positionWS : TEXCOORD1;
                float3 normalWS : TEXCOORD2;
                float3 viewDirWS : TEXCOORD3;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            TEXTURE2D(_MainTex);
            SAMPLER(sampler_MainTex);

            Varyings vert (Attributes input)
            {
                Varyings output;
                
                // GPU Instancing设置
                UNITY_SETUP_INSTANCE_ID(input);
                UNITY_TRANSFER_INSTANCE_ID(input, output);
                
                // 顶点变换
                VertexPositionInputs positionInputs = GetVertexPositionInputs(input.positionOS.xyz);
                VertexNormalInputs normalInputs = GetVertexNormalInputs(input.normalOS);
                
                output.positionCS = positionInputs.positionCS;
                output.positionWS = positionInputs.positionWS;
                output.normalWS = normalInputs.normalWS;
                output.viewDirWS = GetCameraPositionWS() - positionInputs.positionWS;
                output.uv = TRANSFORM_TEX(input.uv, _MainTex);
                
                return output;
            }

            half4 frag (Varyings input) : SV_Target
            {
                // GPU Instancing设置
                UNITY_SETUP_INSTANCE_ID(input);
                
                // 采样主纹理
                half4 texColor = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, input.uv);
                
                // 获取主光源信息
                float4 shadowCoord = TransformWorldToShadowCoord(input.positionWS);
                Light mainLight = GetMainLight(shadowCoord);
                
                // 法线和视线方向
                float3 normalWS = normalize(input.normalWS);
                float3 viewDirWS = normalize(input.viewDirWS);
                
                // Lambert漫反射
                float NdotL = saturate(dot(normalWS, mainLight.direction));
                half3 diffuse = _MainColor.rgb * texColor.rgb * mainLight.color * NdotL;
                
                // Blinn-Phong高光
                float3 halfDir = normalize(mainLight.direction + viewDirWS);
                float NdotH = saturate(dot(normalWS, halfDir));
                float specularPower = _Smoothness * 100.0;
                float specularIntensity = pow(NdotH, specularPower);
                half3 specular = mainLight.color * specularIntensity * _Metallic;
                
                // 应用阴影
                half3 finalColor = (diffuse + specular) * mainLight.shadowAttenuation;
                
                return half4(finalColor, texColor.a);
            }
            ENDHLSL
        }

        // 自定义ShadowCaster Pass - 用于投射阴影
        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull [_Cull]

            HLSLPROGRAM
            #pragma target 3.5
            #pragma vertex ShadowPassVertex
            #pragma fragment ShadowPassFragment

            // 阴影投射关键字
            #pragma multi_compile_vertex _ _CASTING_PUNCTUAL_LIGHT_SHADOW

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Shadows.hlsl"

            // 材质属性
            CBUFFER_START(UnityPerMaterial)
            float4 _MainTex_ST;
            CBUFFER_END

            struct Attributes
            {
                float4 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float2 texcoord : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct Varyings
            {
                float2 uv : TEXCOORD0;
                float4 positionCS : SV_POSITION;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            float4 GetShadowPositionHClip(Attributes input)
            {
                float3 positionWS = TransformObjectToWorld(input.positionOS.xyz);
                float3 normalWS = TransformObjectToWorldNormal(input.normalOS);

            #if _CASTING_PUNCTUAL_LIGHT_SHADOW
                float3 lightDirectionWS = normalize(_LightPosition - positionWS);
            #else
                float3 lightDirectionWS = _LightDirection;
            #endif

                float4 positionCS = TransformWorldToHClip(ApplyShadowBias(positionWS, normalWS, lightDirectionWS));

            #if UNITY_REVERSED_Z
                positionCS.z = min(positionCS.z, UNITY_NEAR_CLIP_VALUE);
            #else
                positionCS.z = max(positionCS.z, UNITY_NEAR_CLIP_VALUE);
            #endif

                return positionCS;
            }

            Varyings ShadowPassVertex(Attributes input)
            {
                Varyings output;
                UNITY_SETUP_INSTANCE_ID(input);
                UNITY_TRANSFER_INSTANCE_ID(input, output);

                output.uv = TRANSFORM_TEX(input.texcoord, _MainTex);
                output.positionCS = GetShadowPositionHClip(input);
                return output;
            }

            half4 ShadowPassFragment(Varyings input) : SV_TARGET
            {
                UNITY_SETUP_INSTANCE_ID(input);
                return 0;
            }

            ENDHLSL
        }

        // 自定义DepthOnly Pass - 用于深度渲染
        Pass
        {
            Name "DepthOnly"
            Tags
            {
                "LightMode" = "DepthOnly"
            }

            ZWrite On
            ColorMask R
            Cull [_Cull]

            HLSLPROGRAM
            #pragma target 3.5
            #pragma vertex DepthOnlyVertex
            #pragma fragment DepthOnlyFragment

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

            // 材质属性
            CBUFFER_START(UnityPerMaterial)
            float4 _MainTex_ST;
            CBUFFER_END

            struct Attributes
            {
                float4 position : POSITION;
                float2 texcoord : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct Varyings
            {
                float2 uv : TEXCOORD0;
                float4 positionCS : SV_POSITION;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            Varyings DepthOnlyVertex(Attributes input)
            {
                Varyings output = (Varyings)0;
                UNITY_SETUP_INSTANCE_ID(input);
                UNITY_TRANSFER_INSTANCE_ID(input, output);

                output.uv = TRANSFORM_TEX(input.texcoord, _MainTex);
                output.positionCS = TransformObjectToHClip(input.position.xyz);
                return output;
            }

            half4 DepthOnlyFragment(Varyings input) : SV_TARGET
            {
                UNITY_SETUP_INSTANCE_ID(input);
                return 0;
            }

            ENDHLSL
        }

        // 自定义DepthNormals Pass - 用于深度法线渲染
        Pass
        {
            Name "DepthNormals"
            Tags
            {
                "LightMode" = "DepthNormals"
            }

            ZWrite On
            Cull [_Cull]

            HLSLPROGRAM
            #pragma target 3.5
            #pragma vertex DepthNormalsVertex
            #pragma fragment DepthNormalsFragment

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

            // 材质属性
            CBUFFER_START(UnityPerMaterial)
            float4 _MainTex_ST;
            CBUFFER_END

            struct Attributes
            {
                float4 position : POSITION;
                float4 tangent : TANGENT;
                float3 normal : NORMAL;
                float2 texcoord : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float2 uv : TEXCOORD1;
                float3 normalWS : TEXCOORD2;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            Varyings DepthNormalsVertex(Attributes input)
            {
                Varyings output = (Varyings)0;
                UNITY_SETUP_INSTANCE_ID(input);
                UNITY_TRANSFER_INSTANCE_ID(input, output);

                output.uv = TRANSFORM_TEX(input.texcoord, _MainTex);
                output.positionCS = TransformObjectToHClip(input.position.xyz);

                VertexNormalInputs normalInput = GetVertexNormalInputs(input.normal, input.tangent);
                output.normalWS = normalInput.normalWS;
                return output;
            }

            half4 DepthNormalsFragment(Varyings input) : SV_TARGET
            {
                UNITY_SETUP_INSTANCE_ID(input);

                float3 normalWS = normalize(input.normalWS);
                return half4(PackNormalOctRectEncode(TransformWorldToViewDir(normalWS, true)), 0.0, 0.0);
            }

            ENDHLSL
        }
    }

    // 自定义FallBack，避免使用官方Diffuse增加变体
    FallBack "Hidden/Universal Render Pipeline/FallbackError"
}
