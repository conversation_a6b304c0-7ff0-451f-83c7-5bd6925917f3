/*
 * Unity URP 透明材质着色器模板
 * 功能：专门用于透明和半透明材质的URP着色器模板
 * 作者：晚三千
 * 版本：1.0
 * 创建日期：2025-07-09
 * 
 * 特性：
 * - 多种透明混合模式（传统透明、预乘透明、加法等）
 * - 深度排序优化
 * - 透明度裁剪支持
 * - 软粒子效果支持
 * - 双面渲染选项
 */

Shader "Custom/URPTransparent"
{
    Properties
    {
        [MainTexture] _MainTex ("主纹理", 2D) = "white" {}
        [MainColor] _MainColor ("主颜色", Color) = (1,1,1,1)
        _Alpha ("透明度", Range(0.0, 1.0)) = 1.0
        _Cutoff ("透明度裁剪", Range(0.0, 1.0)) = 0.5
        
        [Header(透明混合模式)]
        [Enum(Traditional,0,Premultiplied,1,Additive,2,SoftAdditive,3,Multiply,4,Screen,5)] _BlendMode("混合模式", Float) = 0
        
        [Header(渲染选项)]
        [ToggleUI] _DoubleSided("双面渲染", Float) = 0
        [ToggleUI] _AlphaClip("透明度裁剪", Float) = 0
        [ToggleUI] _ReceiveShadows("接收阴影", Float) = 1
        
        [Header(软粒子)]
        [ToggleUI] _SoftParticles("软粒子", Float) = 0
        _SoftParticlesFactor("软粒子因子", Range(0.01, 3.0)) = 1.0
    }

    SubShader
    {
        Tags 
        { 
            "RenderPipeline"="UniversalPipeline"
            "RenderType"="Transparent"
            "Queue"="Transparent"
            "IgnoreProjector"="True"
        }

        Pass
        {
            Name "ForwardLit"
            Tags { "LightMode"="UniversalForward" }

            // 透明混合设置
            Blend SrcAlpha OneMinusSrcAlpha // 默认传统透明
            ZWrite Off
            ZTest LEqual
            Cull [_DoubleSided]

            HLSLPROGRAM
            #pragma target 3.5
            #pragma vertex vert
            #pragma fragment frag

            // 透明相关关键字
            #pragma multi_compile _ _MAIN_LIGHT_SHADOWS
            #pragma multi_compile _ _SHADOWS_SOFT
            #pragma multi_compile _ _ADDITIONAL_LIGHTS
            #pragma multi_compile_fragment _ _SCREEN_SPACE_OCCLUSION
            
            // 自定义关键字
            #pragma shader_feature_local _ALPHACLIP_ON
            #pragma shader_feature_local _DOUBLESIDED_ON
            #pragma shader_feature_local _RECEIVESHADOWS_ON
            #pragma shader_feature_local _SOFTPARTICLES_ON

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DeclareDepthTexture.hlsl"

            CBUFFER_START(UnityPerMaterial)
            half4 _MainColor;
            float4 _MainTex_ST;
            half _Alpha;
            half _Cutoff;
            float _BlendMode;
            half _SoftParticlesFactor;
            CBUFFER_END

            struct Attributes
            {
                float4 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float2 uv : TEXCOORD0;
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float2 uv : TEXCOORD0;
                float3 positionWS : TEXCOORD1;
                float3 normalWS : TEXCOORD2;
                float4 screenPos : TEXCOORD3;
            };

            TEXTURE2D(_MainTex);
            SAMPLER(sampler_MainTex);

            // 混合模式函数
            half3 ApplyBlendMode(half3 baseColor, half3 blendColor, float blendMode)
            {
                if (blendMode < 0.5) // Traditional
                    return baseColor;
                else if (blendMode < 1.5) // Premultiplied
                    return baseColor;
                else if (blendMode < 2.5) // Additive
                    return baseColor + blendColor;
                else if (blendMode < 3.5) // Soft Additive
                    return baseColor + blendColor * 0.5;
                else if (blendMode < 4.5) // Multiply
                    return baseColor * blendColor;
                else // Screen
                    return 1.0 - (1.0 - baseColor) * (1.0 - blendColor);
            }

            Varyings vert(Attributes input)
            {
                Varyings output;
                
                VertexPositionInputs positionInputs = GetVertexPositionInputs(input.positionOS.xyz);
                VertexNormalInputs normalInputs = GetVertexNormalInputs(input.normalOS);
                
                output.positionCS = positionInputs.positionCS;
                output.positionWS = positionInputs.positionWS;
                output.normalWS = normalInputs.normalWS;
                output.uv = TRANSFORM_TEX(input.uv, _MainTex);
                output.screenPos = ComputeScreenPos(output.positionCS);
                
                return output;
            }

            half4 frag(Varyings input) : SV_Target
            {
                // 采样纹理
                half4 texColor = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, input.uv);
                half4 color = texColor * _MainColor;
                color.a *= _Alpha;
                
                // 透明度裁剪
                #ifdef _ALPHACLIP_ON
                clip(color.a - _Cutoff);
                #endif
                
                // 软粒子效果
                #ifdef _SOFTPARTICLES_ON
                float2 screenUV = input.screenPos.xy / input.screenPos.w;
                float sceneDepth = LinearEyeDepth(SampleSceneDepth(screenUV), _ZBufferParams);
                float particleDepth = LinearEyeDepth(input.positionCS.z, _ZBufferParams);
                float depthFade = saturate((sceneDepth - particleDepth) / _SoftParticlesFactor);
                color.a *= depthFade;
                #endif
                
                // 光照计算
                #ifdef _RECEIVESHADOWS_ON
                float4 shadowCoord = TransformWorldToShadowCoord(input.positionWS);
                Light mainLight = GetMainLight(shadowCoord);
                #else
                Light mainLight = GetMainLight();
                #endif
                
                float3 normalWS = normalize(input.normalWS);
                
                // 双面法线处理
                #ifdef _DOUBLESIDED_ON
                normalWS = IS_FRONT_VFACE(input.facing, normalWS, -normalWS);
                #endif
                
                float NdotL = saturate(dot(normalWS, mainLight.direction));
                half3 lighting = mainLight.color * NdotL;
                
                #ifdef _RECEIVESHADOWS_ON
                lighting *= mainLight.shadowAttenuation;
                #endif
                
                // 环境光
                half3 ambient = SampleSH(normalWS);
                
                color.rgb *= lighting + ambient;
                
                return color;
            }
            ENDHLSL
        }
    }
    
    // 透明材质通常不需要阴影投射
    FallBack "Hidden/Universal Render Pipeline/FallbackError"
}
